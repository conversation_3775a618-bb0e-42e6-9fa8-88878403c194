'use client';

import { useState } from 'react';
import VoiceRecorder from '@/components/VoiceRecorder';
import ResultsPage from '@/components/ResultsPage';
import { AnalysisSettings } from '@/types/analysis';
import AuthModal from '../AuthModal';

interface GuestPassViewProps {
  accessCode: string;
}

export default function GuestPassView({ accessCode }: GuestPassViewProps) {
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [analysisSettings, setAnalysisSettings] = useState<AnalysisSettings | null>(null);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  const handleRecordingComplete = (blob: Blob, settings: AnalysisSettings) => {
    setAudioBlob(blob);
    setAnalysisSettings(settings);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Guest Status Banner */}
      <div className="bg-gradient-to-r from-amber-50/50 to-yellow-50/50 border-b border-amber-100/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-amber-500 text-lg">👋</span>
              <span className="text-sm text-amber-700 font-light">
                You're using a guest pass.
                <button
                  onClick={() => setIsAuthModalOpen(true)}
                  className="ml-1 text-amber-800 underline hover:text-amber-600 transition-colors font-medium"
                >
                  Sign up for unlimited access
                </button>
              </span>
            </div>
            <span className="text-xs text-amber-600 bg-amber-100/70 px-3 py-1 rounded-full font-medium">
              Guest
            </span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {!audioBlob ? (
          <VoiceRecorder
            onRecordingComplete={handleRecordingComplete}
            isProcessing={false}
          />
        ) : (
          <ResultsPage audioBlob={audioBlob} settings={analysisSettings!} />
        )}
      </div>

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialMode="sign_up"
      />
    </div>
  );
}
