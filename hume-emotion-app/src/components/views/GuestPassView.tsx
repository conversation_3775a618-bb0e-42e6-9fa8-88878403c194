'use client';

import { useState } from 'react';
import VoiceRecorder from '@/components/VoiceRecorder';
import ResultsPage from '@/components/ResultsPage';
import { AnalysisSettings } from '@/types/analysis';
import AuthModal from '../AuthModal';

interface GuestPassViewProps {
  accessCode: string;
}

export default function GuestPassView({ accessCode }: GuestPassViewProps) {
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [analysisSettings, setAnalysisSettings] = useState<AnalysisSettings | null>(null);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  const handleRecordingComplete = (blob: Blob, settings: AnalysisSettings) => {
    setAudioBlob(blob);
    setAnalysisSettings(settings);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Guest Status Banner */}
      <div className="bg-yellow-50 border-b border-yellow-200">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-yellow-600">👋</span>
              <span className="text-sm text-yellow-800">
                You're using a guest pass. 
                <button
                  onClick={() => setIsAuthModalOpen(true)}
                  className="ml-1 text-yellow-900 underline hover:text-yellow-700"
                >
                  Sign up for unlimited access
                </button>
              </span>
            </div>
            <span className="text-xs text-yellow-600 bg-yellow-100 px-2 py-1 rounded">
              Guest
            </span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {!audioBlob ? (
          <VoiceRecorder
            onRecordingComplete={handleRecordingComplete}
            isProcessing={false}
          />
        ) : (
          <ResultsPage audioBlob={audioBlob} settings={analysisSettings!} />
        )}
      </div>

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialMode="sign_up"
      />
    </div>
  );
}
