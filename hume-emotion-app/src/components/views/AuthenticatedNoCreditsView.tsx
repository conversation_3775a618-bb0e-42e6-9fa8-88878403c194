'use client';

import { useState } from 'react';
import VoiceRecorder from '@/components/VoiceRecorder';
import ResultsPage from '@/components/ResultsPage';
import { AnalysisSettings } from '@/types/analysis';

interface AuthenticatedNoCreditsViewProps {
  user: any;
  profile: any;
}

export default function AuthenticatedNoCreditsView({ user, profile }: AuthenticatedNoCreditsViewProps) {
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [analysisSettings, setAnalysisSettings] = useState<AnalysisSettings | null>(null);

  const handleRecordingComplete = (blob: Blob, settings: AnalysisSettings) => {
    setAudioBlob(blob);
    setAnalysisSettings(settings);
  };

  const handleSubscribe = () => {
    // TODO: Implement Stripe subscription flow
    alert('Subscription flow coming soon!');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Subscription Required Banner */}
      <div className="bg-blue-50 border-b border-blue-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-blue-600">💳</span>
              <div>
                <p className="text-sm font-medium text-blue-900">
                  Welcome back, {user.email}!
                </p>
                <p className="text-xs text-blue-700">
                  Subscribe to start analyzing your voice emotions
                </p>
              </div>
            </div>
            <button
              onClick={handleSubscribe}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              Subscribe Now
            </button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Subscription Prompt */}
        <div className="max-w-2xl mx-auto text-center mb-8">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-4xl mb-4">🚀</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to unlock your emotional insights?
            </h2>
            <p className="text-gray-600 mb-6">
              Join our alpha program for unlimited voice emotion analysis
            </p>
            
            {/* Pricing Card */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6">
              <div className="flex items-center justify-center mb-4">
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  Alpha User Pricing
                </span>
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">
                $20<span className="text-lg font-normal text-gray-600">/month</span>
              </div>
              <ul className="text-sm text-gray-600 space-y-2 mb-6">
                <li className="flex items-center">
                  <svg className="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Unlimited voice analysis
                </li>
                <li className="flex items-center">
                  <svg className="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Analysis history & insights
                </li>
                <li className="flex items-center">
                  <svg className="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Generate access codes for friends
                </li>
                <li className="flex items-center">
                  <svg className="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Early access to new features
                </li>
              </ul>
              <button
                onClick={handleSubscribe}
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors font-medium"
              >
                Start Alpha Subscription
              </button>
            </div>
            
            <p className="text-xs text-gray-500">
              Cancel anytime • No long-term commitment • Alpha pricing guaranteed for early users
            </p>
          </div>
        </div>

        {/* Disabled Voice Recorder (for preview) */}
        <div className="opacity-50 pointer-events-none">
          <VoiceRecorder
            onRecordingComplete={handleRecordingComplete}
            isProcessing={false}
          />
        </div>
      </div>
    </div>
  );
}
