'use client';

import { useState } from 'react';
import AuthModal from '../AuthModal';

export default function AnonymousView() {
  const [accessCode, setAccessCode] = useState('');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  const handleAccessCodeSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (accessCode.trim()) {
      // Redirect with access code
      window.location.href = `/?accessCode=${encodeURIComponent(accessCode.trim())}`;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          {/* Hero Section */}
          <div className="mb-12">
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              🎤 Voice Emotion Analysis
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Discover the emotions hidden in your voice with AI-powered analysis
            </p>
          </div>

          {/* Access Options */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              Get Started
            </h2>
            
            {/* Access Code Form */}
            <form onSubmit={handleAccessCodeSubmit} className="mb-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <input
                  type="text"
                  value={accessCode}
                  onChange={(e) => setAccessCode(e.target.value)}
                  placeholder="Enter access code"
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  type="submit"
                  disabled={!accessCode.trim()}
                  className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  Continue
                </button>
              </div>
            </form>

            <div className="relative mb-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">or</span>
              </div>
            </div>

            {/* Sign Up Button */}
            <button
              onClick={() => setIsAuthModalOpen(true)}
              className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-md hover:from-blue-700 hover:to-indigo-700 transition-all transform hover:scale-105"
            >
              Join Alpha Program - $20/month
            </button>
            
            <p className="text-sm text-gray-500 mt-3">
              Unlimited voice analysis • Alpha user pricing • Cancel anytime
            </p>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
            <div className="bg-white rounded-lg p-6 shadow-md">
              <div className="text-2xl mb-3">🎯</div>
              <h3 className="font-semibold text-gray-900 mb-2">Emotion Detection</h3>
              <p className="text-gray-600 text-sm">
                Advanced AI analyzes your voice to detect subtle emotional patterns
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-md">
              <div className="text-2xl mb-3">📊</div>
              <h3 className="font-semibold text-gray-900 mb-2">Deep Insights</h3>
              <p className="text-gray-600 text-sm">
                Get detailed analysis of your emotional state and patterns
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-md">
              <div className="text-2xl mb-3">🔒</div>
              <h3 className="font-semibold text-gray-900 mb-2">Private & Secure</h3>
              <p className="text-gray-600 text-sm">
                Your voice data is processed securely and never stored permanently
              </p>
            </div>
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialMode="sign_up"
      />
    </div>
  );
}
