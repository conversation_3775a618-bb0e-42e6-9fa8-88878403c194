'use client';

import { useState } from 'react';
import AuthModal from '../AuthModal';

export default function AnonymousView() {
  const [accessCode, setAccessCode] = useState('');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  const handleAccessCodeSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (accessCode.trim()) {
      // Redirect with access code
      window.location.href = `/?accessCode=${encodeURIComponent(accessCode.trim())}`;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          {/* Hero Section */}
          <div className="mb-12">
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              🎤 Voice Emotion Analysis
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Discover the emotions hidden in your voice with AI-powered analysis
            </p>
          </div>

          {/* Access Options */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-sm border border-white/20 p-8 mb-8">
            <h2 className="text-2xl font-light text-gray-800 mb-8 text-center">
              Get Started
            </h2>

            {/* Access Code Form */}
            <form onSubmit={handleAccessCodeSubmit} className="mb-8">
              <div className="flex flex-col sm:flex-row gap-3">
                <input
                  type="text"
                  value={accessCode}
                  onChange={(e) => setAccessCode(e.target.value)}
                  placeholder="Enter access code"
                  className="flex-1 px-5 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-400/30 focus:border-blue-300 transition-all bg-white/50 text-gray-700 placeholder-gray-400"
                />
                <button
                  type="submit"
                  disabled={!accessCode.trim()}
                  className="px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl hover:from-blue-600 hover:to-blue-700 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md"
                >
                  Continue
                </button>
              </div>
            </form>

            <div className="relative mb-8">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white/70 text-gray-400 font-light">or</span>
              </div>
            </div>

            {/* Sign Up Button */}
            <button
              onClick={() => setIsAuthModalOpen(true)}
              className="w-full px-8 py-4 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white rounded-2xl hover:from-indigo-600 hover:via-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg font-medium"
            >
              Join Alpha Program - $20/month
            </button>

            <p className="text-sm text-gray-400 mt-4 text-center font-light">
              Unlimited voice analysis • Alpha user pricing • Cancel anytime
            </p>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/30 hover:bg-white/80 transition-all duration-300">
              <div className="text-3xl mb-4 opacity-80">🎯</div>
              <h3 className="font-medium text-gray-800 mb-3">Emotion Detection</h3>
              <p className="text-gray-500 text-sm leading-relaxed font-light">
                Advanced AI analyzes your voice to detect subtle emotional patterns
              </p>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/30 hover:bg-white/80 transition-all duration-300">
              <div className="text-3xl mb-4 opacity-80">📊</div>
              <h3 className="font-medium text-gray-800 mb-3">Deep Insights</h3>
              <p className="text-gray-500 text-sm leading-relaxed font-light">
                Get detailed analysis of your emotional state and patterns
              </p>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/30 hover:bg-white/80 transition-all duration-300">
              <div className="text-3xl mb-4 opacity-80">🔒</div>
              <h3 className="font-medium text-gray-800 mb-3">Private & Secure</h3>
              <p className="text-gray-500 text-sm leading-relaxed font-light">
                Your voice data is processed securely and never stored permanently
              </p>
            </div>
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialMode="sign_up"
      />
    </div>
  );
}
