'use client';

import { useState } from 'react';
import VoiceRecorder from '@/components/VoiceRecorder';
import ResultsPage from '@/components/ResultsPage';
import { AnalysisSettings } from '@/types/analysis';

interface AuthenticatedWithCreditsViewProps {
  user: any;
  profile: any;
}

export default function AuthenticatedWithCreditsView({ user, profile }: AuthenticatedWithCreditsViewProps) {
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [analysisSettings, setAnalysisSettings] = useState<AnalysisSettings | null>(null);

  const handleRecordingComplete = (blob: Blob, settings: AnalysisSettings) => {
    setAudioBlob(blob);
    setAnalysisSettings(settings);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Subscriber Status Banner */}
      <div className="bg-green-50 border-b border-green-200">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-green-600">✨</span>
              <span className="text-sm text-green-800">
                Welcome back, {user.email}! You have unlimited analysis access.
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">
                Alpha Subscriber
              </span>
              {profile?.isAlphaUser && (
                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                  Alpha Pricing Locked
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {!audioBlob ? (
          <VoiceRecorder
            onRecordingComplete={handleRecordingComplete}
            isProcessing={false}
          />
        ) : (
          <ResultsPage audioBlob={audioBlob} settings={analysisSettings!} />
        )}
      </div>
    </div>
  );
}
