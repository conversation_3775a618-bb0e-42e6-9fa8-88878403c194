'use client';

import { useState, useEffect } from 'react';
import { useSupabase } from './SupabaseProvider';
import { Auth } from '@supabase/auth-ui-react';
import { ThemeSupa } from '@supabase/auth-ui-shared';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialMode?: 'sign_in' | 'sign_up';
}

export default function AuthModal({ isOpen, onClose, initialMode = 'sign_in' }: AuthModalProps) {
  const { supabase, session } = useSupabase();
  const [mode, setMode] = useState<'sign_in' | 'sign_up'>(initialMode);

  // Close modal when user successfully authenticates
  useEffect(() => {
    if (session) {
      onClose();
    }
  }, [session, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Header */}
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {mode === 'sign_up' ? 'Create Account' : 'Welcome Back'}
            </h2>
            <p className="text-gray-600">
              {mode === 'sign_up' 
                ? 'Join our alpha program for unlimited voice analysis'
                : 'Sign in to access your analysis history'
              }
            </p>
          </div>

          {/* Auth Form */}
          <Auth
            supabaseClient={supabase}
            view={mode}
            appearance={{
              theme: ThemeSupa,
              variables: {
                default: {
                  colors: {
                    brand: '#2563eb',
                    brandAccent: '#1d4ed8',
                  },
                },
              },
              className: {
                container: 'w-full',
                button: 'w-full px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors',
                input: 'appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                label: 'block text-sm font-medium text-gray-700 mb-1',
                message: 'text-sm text-red-600 mt-1',
              },
            }}
            providers={['google']}
            redirectTo={`${window.location.origin}/auth/callback`}
            showLinks={false}
            localization={{
              variables: {
                sign_in: {
                  email_label: 'Email address',
                  password_label: 'Password',
                  button_label: 'Sign In',
                  loading_button_label: 'Signing in...',
                  social_provider_text: 'Continue with {{provider}}',
                },
                sign_up: {
                  email_label: 'Email address',
                  password_label: 'Password',
                  button_label: 'Create Account',
                  loading_button_label: 'Creating account...',
                  social_provider_text: 'Continue with {{provider}}',
                },
              },
            }}
          />

          {/* Mode Toggle */}
          <div className="mt-6 text-center">
            <button
              onClick={() => setMode(mode === 'sign_in' ? 'sign_up' : 'sign_in')}
              className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
            >
              {mode === 'sign_in' 
                ? "Don't have an account? Create one"
                : "Already have an account? Sign in"
              }
            </button>
          </div>

          {/* Alpha Program Notice */}
          {mode === 'sign_up' && (
            <div className="mt-4 p-3 bg-blue-50 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-700">
                    <strong>Alpha User Pricing:</strong> $20/month for unlimited analysis during our alpha phase.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
